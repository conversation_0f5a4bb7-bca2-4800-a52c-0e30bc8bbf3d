#!/usr/bin/env python3
"""
TransformerForDiffusion使用示例

展示如何在你的代码中创建、加载和使用TransformerForDiffusion模型
"""

import torch
import torch.nn as nn
from pathlib import Path

# 导入LeRobot模块
from lerobot.common.policies.diffusion.modeling_diffusion import TransformerForDiffusion
from lerobot.configs.policies import PreTrainedConfig


class DiffusionTransformerWrapper:
    """
    TransformerForDiffusion的包装类
    简化模型的创建、加载和使用
    """
    
    def __init__(self, checkpoint_path: str, weights_path: str = None, device: str = "cpu"):
        self.device = torch.device(device)
        self.transformer = None
        self.config = None
        
        # 加载模型
        self.load_model(checkpoint_path, weights_path)
    
    def load_model(self, checkpoint_path: str, weights_path: str = None):
        """加载TransformerForDiffusion模型"""
        print("🤖 加载TransformerForDiffusion模型...")
        
        # 1. 加载配置
        self.config = PreTrainedConfig.from_pretrained(checkpoint_path)
        
        # 2. 创建模型实例
        cond_dim = 66  # 根据你的具体情况调整
        self.transformer = TransformerForDiffusion(self.config, cond_dim=cond_dim)
        
        # 3. 加载权重
        if weights_path is None:
            weights_path = "./extracted_weights/transformer_weights.pth"
        
        if Path(weights_path).exists():
            weights = torch.load(weights_path, map_location='cpu')
            
            # 只加载参数，忽略缓冲区
            param_weights = {name: weights[name] for name in self.transformer.state_dict() 
                           if name in weights and not name.endswith(('mask', 'memory_mask'))}
            
            self.transformer.load_state_dict(param_weights, strict=False)
            print("✅ 权重加载成功!")
        else:
            print(f"⚠️ 权重文件不存在: {weights_path}")
        
        # 4. 移动到指定设备
        self.transformer.to(self.device)
        self.transformer.eval()
        
        print(f"📱 模型已加载到设备: {self.device}")
    
    def predict(self, sample: torch.Tensor, timestep: torch.Tensor, global_cond: torch.Tensor):
        """
        使用模型进行预测
        
        Args:
            sample: [batch, horizon, action_dim] 输入样本
            timestep: [batch] 时间步
            global_cond: [batch, n_obs_steps * cond_dim] 全局条件
        
        Returns:
            [batch, horizon, action_dim] 预测输出
        """
        with torch.no_grad():
            # 确保输入在正确的设备上
            sample = sample.to(self.device)
            timestep = timestep.to(self.device)
            global_cond = global_cond.to(self.device)
            
            # 前向传播
            output = self.transformer(sample, timestep, global_cond)
            
        return output
    
    def generate_action_sequence(self, observation: torch.Tensor, num_timesteps: int = 100):
        """
        生成动作序列（简化的扩散采样过程）
        
        Args:
            observation: [batch, obs_dim] 观察
            num_timesteps: 扩散步数
            
        Returns:
            [batch, horizon, action_dim] 生成的动作序列
        """
        batch_size = observation.shape[0]
        horizon = self.config.horizon
        action_dim = self.config.action_feature.shape[0]
        n_obs_steps = self.config.n_obs_steps
        
        # 准备全局条件 (这里简化处理，实际应该根据具体情况构造)
        # 假设observation包含图像特征和状态
        obs_repeated = observation.unsqueeze(1).repeat(1, n_obs_steps, 1)  # 重复观察
        global_cond = obs_repeated.flatten(1)  # [batch, n_obs_steps * obs_dim]
        
        # 初始化噪声
        sample = torch.randn(batch_size, horizon, action_dim, device=self.device)
        
        # 简化的扩散采样（实际需要使用调度器）
        for t in range(num_timesteps):
            timestep = torch.full((batch_size,), t, device=self.device)
            
            # 预测噪声
            predicted_noise = self.predict(sample, timestep, global_cond)
            
            # 简单的去噪步骤（实际需要使用DDPM/DDIM调度器）
            alpha = 1.0 - t / num_timesteps
            sample = alpha * sample + (1 - alpha) * predicted_noise
        
        return sample
    
    @property 
    def model_info(self):
        """获取模型信息"""
        return {
            "embedding_dim": self.config.diffusion_step_embed_dim,
            "num_encoder_layers": self.config.n_cond_layers,
            "num_decoder_layers": self.config.n_layer,
            "num_heads": self.config.n_head,
            "horizon": self.config.horizon,
            "n_obs_steps": self.config.n_obs_steps,
            "total_parameters": sum(p.numel() for p in self.transformer.parameters()),
            "device": str(self.device)
        }


def example_1_basic_usage():
    """示例1: 基本使用"""
    print("=== 示例1: 基本使用 ===")
    
    # 创建模型包装器
    checkpoint_path = "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/190000/pretrained_model"
    model = DiffusionTransformerWrapper(
        checkpoint_path=checkpoint_path,
        weights_path="./extracted_weights/transformer_weights.pth",
        device="cpu"  # 或 "cuda" 如果有GPU
    )
    
    # 打印模型信息
    print("模型信息:")
    for key, value in model.model_info.items():
        print(f"  {key}: {value}")
    
    # 创建测试输入
    batch_size = 1
    sample = torch.randn(batch_size, 16, 2)  # [batch, horizon, action_dim]
    timestep = torch.randint(0, 100, (batch_size,))  # [batch]
    global_cond = torch.randn(batch_size, 132)  # [batch, n_obs_steps * cond_dim]
    
    # 预测
    output = model.predict(sample, timestep, global_cond)
    print(f"预测输出形状: {output.shape}")
    print(f"预测统计: min={output.min():.4f}, max={output.max():.4f}")


def example_2_action_generation():
    """示例2: 动作生成"""
    print("\n=== 示例2: 动作生成 ===")
    
    # 创建模型
    checkpoint_path = "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/190000/pretrained_model"
    model = DiffusionTransformerWrapper(checkpoint_path, device="cpu")
    
    # 模拟观察输入（例如：图像特征 + 状态）
    batch_size = 2
    obs_dim = 66  # 64维图像特征 + 2维状态
    observation = torch.randn(batch_size, obs_dim)
    
    # 生成动作序列
    action_sequence = model.generate_action_sequence(observation, num_timesteps=50)
    print(f"生成的动作序列形状: {action_sequence.shape}")
    print(f"动作统计: min={action_sequence.min():.4f}, max={action_sequence.max():.4f}")


def example_3_batch_processing():
    """示例3: 批量处理"""
    print("\n=== 示例3: 批量处理 ===")
    
    # 创建模型
    checkpoint_path = "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/190000/pretrained_model"
    model = DiffusionTransformerWrapper(checkpoint_path, device="cpu")
    
    # 批量输入
    batch_size = 8
    horizon = 16
    action_dim = 2
    
    # 创建批量输入
    samples = torch.randn(batch_size, horizon, action_dim)
    timesteps = torch.randint(0, 100, (batch_size,))
    global_conds = torch.randn(batch_size, 132)
    
    # 批量预测
    outputs = model.predict(samples, timesteps, global_conds)
    
    print(f"批量输入形状: {samples.shape}")
    print(f"批量输出形状: {outputs.shape}")
    print("✅ 批量处理成功!")


def main():
    """主函数 - 运行所有示例"""
    print("🚀 TransformerForDiffusion使用示例\n")
    
    try:
        # 检查必要文件是否存在
        checkpoint_path = "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/190000/pretrained_model"
        weights_path = "./extracted_weights/transformer_weights.pth"
        
        if not Path(checkpoint_path).exists():
            print(f"❌ 检查点路径不存在: {checkpoint_path}")
            return
        
        if not Path(weights_path).exists():
            print(f"❌ 权重文件不存在: {weights_path}")
            print("请先运行 simple_weight_extractor.py 提取权重")
            return
        
        # 运行示例
        example_1_basic_usage()
        example_2_action_generation()
        example_3_batch_processing()
        
        print("\n🎉 所有示例运行完成!")
        print("\n💡 使用提示:")
        print("1. 在实际应用中，你需要根据具体任务调整 cond_dim 和输入格式")
        print("2. 对于真实的扩散采样，建议使用 DDPM 或 DDIM 调度器")
        print("3. 根据需要调整 batch_size 和设备类型")
        
    except Exception as e:
        print(f"❌ 运行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main() 