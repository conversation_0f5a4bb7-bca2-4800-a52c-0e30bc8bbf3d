#!/usr/bin/env python3
"""
简化的TransformerForDiffusion加载器

快速创建和加载TransformerForDiffusion模型
"""

import torch
from pathlib import Path

# 导入LeRobot模块
from lerobot.common.policies.diffusion.modeling_diffusion import TransformerForDiffusion
from lerobot.configs.policies import PreTrainedConfig


def load_transformer_from_checkpoint(checkpoint_path: str, weights_path: str = None):
    """
    从检查点加载TransformerForDiffusion
    
    Args:
        checkpoint_path: 检查点配置路径
        weights_path: 可选的权重文件路径，如果不提供则使用提取的权重
    
    Returns:
        TransformerForDiffusion实例
    """
    print(f"正在加载TransformerForDiffusion...")
    print(f"配置路径: {checkpoint_path}")
    
    # 1. 加载配置
    config = PreTrainedConfig.from_pretrained(checkpoint_path)
    print(f"配置类型: {config.type}")
    
    # 2. 确定条件维度 (根据你的模型：64维图像特征 + 2维状态)
    cond_dim = 66
    
    # 3. 创建TransformerForDiffusion实例
    transformer = TransformerForDiffusion(config, cond_dim=cond_dim)
    print(f"模型创建成功，参数量: {sum(p.numel() for p in transformer.parameters()):,}")
    
    # 4. 加载权重
    if weights_path is None:
        weights_path = "./extracted_weights/transformer_weights.pth"
    
    if Path(weights_path).exists():
        print(f"加载权重: {weights_path}")
        weights = torch.load(weights_path, map_location='cpu')
        
        # 过滤掉缓冲区（buffers），只加载参数
        param_weights = {}
        for name, param in transformer.named_parameters():
            if name in weights:
                param_weights[name] = weights[name]
            else:
                print(f"⚠️ 权重文件中缺少参数: {name}")
        
        # 加载权重（非严格模式，忽略缓冲区）
        missing_keys, unexpected_keys = transformer.load_state_dict(param_weights, strict=False)
        
        if not missing_keys:
            print("✅ 所有参数权重加载成功!")
        else:
            print(f"⚠️ 缺失参数: {missing_keys}")
            
    else:
        print(f"❌ 权重文件不存在: {weights_path}")
        return None
    
    return transformer


def test_transformer(transformer, batch_size=2):
    """测试TransformerForDiffusion前向传播"""
    print(f"\n测试模型前向传播 (batch_size={batch_size})...")
    
    transformer.eval()
    
    # 输入维度
    horizon = 16  # 时间步长
    action_dim = 2  # 动作维度
    n_obs_steps = 2  # 观察步数
    cond_dim = 66  # 条件维度
    
    # 创建随机输入
    sample = torch.randn(batch_size, horizon, action_dim)
    timestep = torch.randint(0, 100, (batch_size,))
    global_cond = torch.randn(batch_size, n_obs_steps * cond_dim)
    
    print(f"输入形状:")
    print(f"  sample: {sample.shape}")
    print(f"  timestep: {timestep.shape}")  
    print(f"  global_cond: {global_cond.shape}")
    
    # 前向传播
    with torch.no_grad():
        output = transformer(sample, timestep, global_cond)
    
    print(f"输出形状: {output.shape}")
    print(f"输出统计: min={output.min():.4f}, max={output.max():.4f}, mean={output.mean():.4f}")
    print("✅ 模型测试成功!")
    
    return output


def save_standalone_model(transformer, save_path: str):
    """保存独立的模型文件"""
    save_path = Path(save_path)
    save_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 保存完整模型
    torch.save({
        'model_state_dict': transformer.state_dict(),
        'model_class': 'TransformerForDiffusion',
        'architecture': {
            'embedding_dim': transformer.config.diffusion_step_embed_dim,
            'num_encoder_layers': transformer.config.n_cond_layers,
            'num_decoder_layers': transformer.config.n_layer,
            'num_heads': transformer.config.n_head,
            'horizon': transformer.config.horizon,
            'n_obs_steps': transformer.config.n_obs_steps,
        }
    }, save_path)
    
    print(f"独立模型已保存到: {save_path}")


def main():
    """主函数"""
    import sys
    
    # 默认路径
    default_checkpoint = "/home/<USER>/work/lerobot-add_transformer/outputs/train/diffusion_pusht_transformer_edit/checkpoints/190000/pretrained_model"
    default_weights = "./extracted_weights/transformer_weights.pth"
    
    if len(sys.argv) == 1:
        # 使用默认路径
        checkpoint_path = default_checkpoint
        weights_path = default_weights
    elif len(sys.argv) == 2:
        # 只指定检查点路径
        checkpoint_path = sys.argv[1]
        weights_path = default_weights
    elif len(sys.argv) == 3:
        # 指定检查点和权重路径
        checkpoint_path = sys.argv[1]
        weights_path = sys.argv[2]
    else:
        print("使用方法:")
        print("  python simple_transformer_loader.py")
        print("  python simple_transformer_loader.py <checkpoint_path>")
        print("  python simple_transformer_loader.py <checkpoint_path> <weights_path>")
        sys.exit(1)
    
    try:
        # 加载模型
        transformer = load_transformer_from_checkpoint(checkpoint_path, weights_path)
        
        if transformer is not None:
            # 测试模型
            test_transformer(transformer)
            
            # 保存独立模型（可选）
            save_standalone_model(transformer, "./loaded_transformer_model.pth")
            
            print(f"\n🎉 TransformerForDiffusion加载完成!")
            print(f"你现在可以使用这个模型进行推理或进一步训练。")
            
            return transformer
        else:
            print("❌ 模型加载失败")
            return None
            
    except Exception as e:
        print(f"❌ 加载过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    transformer = main() 