"""
SoftREPA-style DiffusionModel_DC for TransformerForDiffusion_DC in robot action diffusion.
Based on SoftREPA's contrastive learning approach for improving action-observation alignment.
Extends the existing DiffusionModel class to add DC token functionality.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Optional, Tuple, Dict, Any
import numpy as np
from dataclasses import dataclass

from lerobot.common.policies.diffusion.configuration_diffusion import DiffusionConfig
from lerobot.common.policies.diffusion.modeling_diffusion import (
    DiffusionModel,
    DiffusionPolicy,
    TransformerForDiffusion_DC,
    _make_noise_scheduler,
)
from lerobot.common.policies.utils import (
    get_device_from_parameters,
    get_dtype_from_parameters
)
from lerobot.common.constants import OBS_ROBOT, OBS_ENV

import pdb


class DiffusionModel_DC(DiffusionModel):
    """
    SoftREPA-style DiffusionModel with DC tokens for contrastive learning.

    Extends the base DiffusionModel to support:
    1. DC token injection in TransformerForDiffusion_DC
    2. Contrastive learning for action-observation alignment
    3. Single-step training with batch contrastive loss
    4. Loading from pretrained DiffusionModel checkpoints
    """

    def __init__(self, config: DiffusionConfig, n_dc_tokens: int = None, n_dc_layers: int = None, use_dc_t: bool = None, cond_dim: Optional[int] = None):
        # Store DC configuration
        self.n_dc_tokens = n_dc_tokens or 4
        self.n_dc_layers = n_dc_layers or 6
        self.use_dc_t = use_dc_t if use_dc_t is not None else True

        # Initialize base DiffusionModel
        super().__init__(config)

        # Replace network with DC version if using transformer
        if config.use_transformer and not (hasattr(config, 'use_dit') and config.use_dit) and not (hasattr(config, 'use_mmdit') and config.use_mmdit):
            # Use provided condition dimension or calculate from config
            global_cond_dim = cond_dim or self._calculate_condition_dim()

            # Create DC version
            self.net = TransformerForDiffusion_DC(
                config=config,
                cond_dim=global_cond_dim,
                n_dc_tokens=self.n_dc_tokens,
                n_dc_layers=self.n_dc_layers,
                use_dc_t=self.use_dc_t,
                use_dc=True
            )

            print(f"✅ Created DiffusionModel_DC with {self.n_dc_tokens} DC tokens, {self.n_dc_layers} layers")

    def _calculate_condition_dim(self):
        """Calculate condition dimension from config"""
        global_cond_dim = 0

        # Robot state features
        if hasattr(self.config, 'robot_state_feature') and self.config.robot_state_feature is not None:
            global_cond_dim += self.config.robot_state_feature.shape[0]

        # Image features
        if hasattr(self.config, 'image_features') and self.config.image_features:
            num_images = len(self.config.image_features)
            if hasattr(self, 'rgb_encoder'):
                if self.config.use_separate_rgb_encoder_per_camera:
                    global_cond_dim += self.rgb_encoder[0].feature_dim * num_images
                else:
                    global_cond_dim += self.rgb_encoder.feature_dim * num_images

        # Environment state features
        if hasattr(self.config, 'env_state_feature') and self.config.env_state_feature is not None:
            global_cond_dim += self.config.env_state_feature.shape[0]

        # Default fallback
        if global_cond_dim == 0:
            global_cond_dim = 64
            print(f"Warning: Using default condition dimension: {global_cond_dim}")

        return global_cond_dim



    @classmethod
    def from_pretrained(
        cls,
        pretrained_model_path: str,
        config: DiffusionConfig,
        n_dc_tokens: int = 4,
        n_dc_layers: int = 6,
        use_dc_t: bool = True,
        cond_dim: Optional[int] = None
    ):
        """
        Create DiffusionModel_DC from a pretrained checkpoint.

        Args:
            pretrained_model_path: Path to checkpoint directory
            config: DiffusionConfig for the model
            n_dc_tokens: Number of DC tokens per layer
            n_dc_layers: Number of layers to apply DC tokens
            use_dc_t: Whether to use time-dependent DC tokens
            cond_dim: Optional condition dimension override

        Returns:
            DiffusionModel_DC with pretrained weights loaded
        """
        print(f"🔄 Loading DiffusionModel_DC from: {pretrained_model_path}")

        # Create model instance
        model = cls(config, n_dc_tokens, n_dc_layers, use_dc_t, cond_dim)

        # Try to load pretrained weights
        try:
            model._load_pretrained_weights(pretrained_model_path)
            print("✅ Pretrained weights loaded successfully")
        except Exception as e:
            print(f"⚠️ Could not load pretrained weights: {e}")
            print("Using randomly initialized weights")

        return model

    def _load_pretrained_weights(self, pretrained_model_path: str):
        """Load pretrained weights from checkpoint"""
        from pathlib import Path

        pretrained_path = Path(pretrained_model_path)

        # Find the model file
        if pretrained_path.is_dir():
            model_dir = pretrained_path / "pretrained_model"
            if model_dir.exists():
                model_file = model_dir / "model.safetensors"
                if not model_file.exists():
                    model_file = model_dir / "pytorch_model.bin"
            else:
                model_file = pretrained_path / "model.safetensors"
                if not model_file.exists():
                    model_file = pretrained_path / "pytorch_model.bin"
        else:
            model_file = pretrained_path

        if not model_file.exists():
            raise FileNotFoundError(f"Model file not found: {model_file}")

        # Load weights
        if str(model_file).endswith('.safetensors'):
            try:
                from safetensors.torch import load_file
                pretrained_state_dict = load_file(str(model_file))
            except ImportError:
                raise ImportError("safetensors not available, please install it")
        else:
            pretrained_state_dict = torch.load(model_file, map_location='cpu')
            if 'model_state_dict' in pretrained_state_dict:
                pretrained_state_dict = pretrained_state_dict['model_state_dict']

        # Load matching weights (skip DC tokens)
        model_state_dict = self.state_dict()
        loaded_count = 0

        for name, param in model_state_dict.items():
            # Skip DC-specific parameters
            if 'dc' in name.lower():
                continue

            # Try different name mappings
            for prefix in ['', 'diffusion.', 'net.']:
                full_name = f"{prefix}{name}"
                if full_name in pretrained_state_dict:
                    pretrained_param = pretrained_state_dict[full_name]
                    if param.shape == pretrained_param.shape:
                        param.data.copy_(pretrained_param.data)
                        loaded_count += 1
                        break

        print(f"Loaded {loaded_count} pretrained parameters")

    def forward(self, sample: torch.Tensor, timestep: torch.Tensor, global_cond: torch.Tensor = None) -> torch.Tensor:
        """
        Forward pass through the DC network.

        Args:
            sample: [batch, horizon, action_dim] noisy action samples
            timestep: [batch] diffusion timesteps
            global_cond: [batch, cond_dim] global conditioning

        Returns:
            [batch, horizon, action_dim] predicted noise or denoised sample
        """
        return self.net(sample, timestep, global_cond)

    def initialize_dc_tokens(self):
        """Initialize DC tokens with small random values"""
        if hasattr(self.net, 'dc_tokens'):
            nn.init.normal_(self.net.dc_tokens, mean=0, std=0.02)
            if hasattr(self.net, 'dc_t_tokens') and self.net.dc_t_tokens is not None:
                nn.init.normal_(self.net.dc_t_tokens.weight, mean=0, std=0.02)

    def freeze_base_model(self):
        """Freeze all parameters except DC tokens (for SoftREPA training)"""
        for name, param in self.named_parameters():
            if 'dc' in name:
                param.requires_grad = True
            else:
                param.requires_grad = False

    def get_dc_parameters(self) -> List[torch.nn.Parameter]:
        """Get only DC-related parameters for optimization"""
        dc_params = []
        for name, param in self.named_parameters():
            if 'dc' in name and param.requires_grad:
                dc_params.append(param)
        return dc_params

    def print_parameter_stats(self):
        """Print detailed parameter statistics"""
        total_params = 0
        trainable_params = 0
        dc_params = 0

        print("Parameter Statistics:")
        print("-" * 50)

        for name, param in self.named_parameters():
            param_count = param.numel()
            total_params += param_count

            if param.requires_grad:
                trainable_params += param_count

            if 'dc' in name:
                dc_params += param_count
                print(f"DC Parameter: {name}")
                print(f"  Shape: {param.shape}")
                print(f"  Count: {param_count:,}")
                print(f"  Trainable: {param.requires_grad}")

        print("-" * 50)
        print(f"Total parameters: {total_params:,}")
        print(f"Trainable parameters: {trainable_params:,}")
        print(f"DC parameters: {dc_params:,}")
        print(f"Frozen parameters: {total_params - trainable_params:,}")
        print(f"Training efficiency: {trainable_params/total_params*100:.2f}% of total params")

    def compute_loss(self, batch):
        return super().compute_loss(batch)



    def compute_contrastive_error(self, batch: dict[str, torch.Tensor], use_dc: bool = True) -> torch.Tensor:
        """
        Compute contrastive error matrix for SoftREPA training.
        Improved version based on lerobot_dc_loss implementation.

        Args:
            batch: Dictionary containing 'action' and observation data
            use_dc: Whether to use DC tokens

        Returns:
            (B, B) error matrix where error[i,j] is the prediction error
            between action_i and observation_j
        """
        batch_size = batch["action"].shape[0]

        # Choose implementation based on batch size for efficiency
        if batch_size <= 8:
            # Small batch: use loop-based method for clarity and memory efficiency
            return self._compute_contrastive_error_loop(batch, use_dc)
        else:
            # Large batch: use vectorized method for speed
            return self._compute_contrastive_error_vectorized(batch, use_dc)

    def _compute_contrastive_error_loop(self, batch: dict[str, torch.Tensor], use_dc: bool = True) -> torch.Tensor:
        """
        Loop-based implementation for small batches.
        More memory efficient and easier to debug.
        """
        batch_size = batch["action"].shape[0]
        error_matrix = torch.zeros(batch_size, batch_size, device=batch["action"].device)

        # Identify observation keys
        observation_keys = [key for key in batch.keys() if key.startswith("observation.")]

        # Store original mask setting
        original_mask_setting = getattr(self.config, 'do_mask_loss_for_padding', False)
        self.config.do_mask_loss_for_padding = False

        try:
            # Set DC usage
            if hasattr(self.net, 'use_dc'):
                self.net.use_dc = use_dc

            for i in range(batch_size):      # action index
                for j in range(batch_size):  # observation index
                    # Create paired batch: action_i with observation_j
                    paired_batch = self._create_paired_batch(batch, i, j, observation_keys)

                    # Compute diffusion loss for this pair
                    loss = self._compute_single_diffusion_loss(paired_batch)
                    error_matrix[i, j] = loss.item()

        finally:
            # Restore original setting
            self.config.do_mask_loss_for_padding = original_mask_setting

        return error_matrix

    def _compute_contrastive_error_vectorized(self, batch: dict[str, torch.Tensor], use_dc: bool = True) -> torch.Tensor:
        """
        Vectorized implementation for large batches.
        More computationally efficient but uses more memory.
        """
        # Preprocess batch to add observation.images if needed
        if self.config.image_features:
            batch = dict(batch)  # shallow copy
            batch["observation.images"] = torch.stack(
                [batch[key] for key in self.config.image_features], dim=-4
            )

        batch_size = batch["action"].shape[0]

        # Identify observation keys
        observation_keys = [key for key in batch.keys() if key.startswith("observation.")]

        # Create expanded batch for all action-observation pairs
        expanded_batch = self._create_expanded_batch(batch, observation_keys)

        # Store original mask setting
        original_mask_setting = getattr(self.config, 'do_mask_loss_for_padding', False)
        self.config.do_mask_loss_for_padding = False

        try:
            # Set DC usage
            if hasattr(self.net, 'use_dc'):
                self.net.use_dc = use_dc

            # Compute losses for all pairs at once
            losses = self._compute_batch_diffusion_losses(expanded_batch)  # (B*B,)
            error_matrix = losses.reshape(batch_size, batch_size)  # (B, B)

        finally:
            # Restore original setting
            self.config.do_mask_loss_for_padding = original_mask_setting

        return error_matrix

    def _create_paired_batch(self, original_batch: dict[str, torch.Tensor],
                           action_idx: int, obs_idx: int,
                           observation_keys: list) -> dict[str, torch.Tensor]:
        """
        Create a batch with action_idx paired with observation_obs_idx.
        Based on lerobot_dc_loss implementation.
        """
        paired_batch = {}

        # Action: take the action_idx-th sample
        paired_batch["action"] = original_batch["action"][action_idx:action_idx+1]

        # Action padding mask if exists
        if "action_is_pad" in original_batch:
            paired_batch["action_is_pad"] = original_batch["action_is_pad"][action_idx:action_idx+1]

        # Observations: take the obs_idx-th sample
        for key in observation_keys:
            paired_batch[key] = original_batch[key][obs_idx:obs_idx+1]

        return paired_batch

    def _create_expanded_batch(self, batch: dict[str, torch.Tensor],
                             observation_keys: list) -> dict[str, torch.Tensor]:
        """
        Create expanded batch for vectorized computation.
        Based on lerobot_dc_loss efficient implementation.
        """
        batch_size = batch["action"].shape[0]

        # Expand actions: each action repeated for all observations
        actions = batch["action"]  # (B, horizon, action_dim)
        expanded_actions = actions.unsqueeze(1).repeat(1, batch_size, 1, 1)  # (B, B, horizon, action_dim)
        expanded_actions = expanded_actions.reshape(batch_size * batch_size, *actions.shape[1:])  # (B*B, horizon, action_dim)

        # Expand observations: each observation repeated for all actions
        expanded_observations = {}
        for key in observation_keys:
            obs = batch[key]  # (B, ...)
            expanded_obs = obs.unsqueeze(0).repeat(batch_size, 1, *([1] * (obs.dim() - 1)))  # (B, B, ...)
            expanded_observations[key] = expanded_obs.reshape(batch_size * batch_size, *obs.shape[1:])  # (B*B, ...)

        # Handle action padding mask
        expanded_action_is_pad = None
        if "action_is_pad" in batch:
            action_is_pad = batch["action_is_pad"]  # (B, horizon)
            expanded_action_is_pad = action_is_pad.unsqueeze(1).repeat(1, batch_size, 1)  # (B, B, horizon)
            expanded_action_is_pad = expanded_action_is_pad.reshape(batch_size * batch_size, action_is_pad.shape[1])  # (B*B, horizon)

        # Construct expanded batch
        expanded_batch = {
            "action": expanded_actions,
            **expanded_observations
        }
        if expanded_action_is_pad is not None:
            expanded_batch["action_is_pad"] = expanded_action_is_pad

        return expanded_batch

    def _compute_single_diffusion_loss(self, batch: dict[str, torch.Tensor]) -> torch.Tensor:
        """
        Compute diffusion loss for a single action-observation pair.
        Based on the original compute_loss method but for single samples.
        """
        # Preprocess batch to add observation.images if needed
        if self.config.image_features:
            batch = dict(batch)  # shallow copy
            batch["observation.images"] = torch.stack(
                [batch[key] for key in self.config.image_features], dim=-4
            )

        # Prepare global conditioning
        global_cond = self._prepare_global_conditioning(batch)

        # Forward diffusion process
        trajectory = batch["action"]
        eps = torch.randn(trajectory.shape, device=trajectory.device)
        timesteps = torch.randint(
            low=0,
            high=self.noise_scheduler.config.num_train_timesteps,
            size=(trajectory.shape[0],),
            device=trajectory.device,
        ).long()
        noisy_trajectory = self.noise_scheduler.add_noise(trajectory, eps, timesteps)

        # Network prediction
        pred = self.net(noisy_trajectory, timesteps, global_cond=global_cond)

        # Compute target
        if self.config.prediction_type == "epsilon":
            target = eps
        elif self.config.prediction_type == "sample":
            target = batch["action"]
        else:
            raise ValueError(f"Unsupported prediction type {self.config.prediction_type}")

        # Compute loss (mean over all dimensions)
        loss = F.mse_loss(pred, target, reduction='mean')

        return loss

    def _compute_batch_diffusion_losses(self, batch: dict[str, torch.Tensor]) -> torch.Tensor:
        """
        Compute diffusion losses for a batch of action-observation pairs.
        Returns per-sample losses without averaging across the batch.
        """
        # Preprocess batch to add observation.images if needed
        if self.config.image_features:
            batch = dict(batch)  # shallow copy
            batch["observation.images"] = torch.stack(
                [batch[key] for key in self.config.image_features], dim=-4
            )

        # Prepare global conditioning
        global_cond = self._prepare_global_conditioning(batch)

        # Forward diffusion process
        trajectory = batch["action"]
        eps = torch.randn(trajectory.shape, device=trajectory.device)
        timesteps = torch.randint(
            low=0,
            high=self.noise_scheduler.config.num_train_timesteps,
            size=(trajectory.shape[0],),
            device=trajectory.device,
        ).long()
        noisy_trajectory = self.noise_scheduler.add_noise(trajectory, eps, timesteps)

        # Network prediction
        pred = self.net(noisy_trajectory, timesteps, global_cond=global_cond)

        # Compute target
        if self.config.prediction_type == "epsilon":
            target = eps
        elif self.config.prediction_type == "sample":
            target = batch["action"]
        else:
            raise ValueError(f"Unsupported prediction type {self.config.prediction_type}")

        # Compute per-sample losses (no reduction across batch)
        loss = F.mse_loss(pred, target, reduction='none')  # (B, horizon, action_dim)
        loss_per_sample = loss.mean(dim=(1, 2))  # (B,) - average over horizon and action_dim

        return loss_per_sample


class ContrastiveLoss(nn.Module):
    """
    Contrastive loss for action-observation alignment.
    Adapted from SoftREPA for robot action sequences.
    """
    
    def __init__(self, temp: float = 0.07, scale: float = 4.0, dweight: float = 0.0, device: str = 'cuda'):
        super().__init__()
        self.device = device
        self.temp = nn.Parameter(torch.tensor(temp, device=device))
        self.scale = nn.Parameter(torch.tensor(scale, device=device))
        self.dweight = dweight  # diffusion loss weight
    
    def get_mask(self, shape: Tuple[int, int]) -> torch.Tensor:
        """Create positive sample mask (diagonal)"""
        mask = torch.zeros(shape, device=self.device)
        n_b, _ = shape
        index = torch.arange(n_b, device=self.device)
        mask[index, index] = 1
        return mask
    
    def forward(self, errors: torch.Tensor) -> torch.Tensor:
        """
        Compute contrastive loss from prediction errors.

        Args:
            errors: (B, B) error matrix where errors[i,j] is the error
                   between action_i and condition_j

        Returns:
            Scalar contrastive loss
        """
        batch_size = errors.shape[0]
    
        # Convert errors to similarity scores (lower error = higher similarity)
        # Following SoftREPA: logits = scale * exp(-errors/temp)
        logits = self.scale * torch.exp(-errors / self.temp)  # (B, B)

        # Create target labels (diagonal elements are positive pairs)
        targets = torch.arange(batch_size, device=errors.device)  # [0, 1, 2, ..., B-1]

        # Cross-entropy loss for contrastive learning
        # Each row should predict its corresponding diagonal element
        loss = F.cross_entropy(logits, targets)

        # Optional: add diffusion loss weight for diagonal elements
        if self.dweight > 0:
            diagonal_errors = torch.diag(errors)
            loss += self.dweight * diagonal_errors.mean()

        return loss

    def compute_accuracy(self, errors: torch.Tensor) -> torch.Tensor:
        """
        Compute contrastive accuracy (how often the diagonal is the minimum in each row).

        Args:
            errors: (B, B) error matrix

        Returns:
            Scalar accuracy between 0 and 1
        """
        batch_size = errors.shape[0]

        # For each row, check if diagonal element has minimum error
        min_indices = torch.argmin(errors, dim=1)  # (B,)
        correct_predictions = (min_indices == torch.arange(batch_size, device=errors.device))
        accuracy = correct_predictions.float().mean()

        return accuracy

    def get_diagnostics(self, errors: torch.Tensor) -> dict:
        """
        Get diagnostic information about the contrastive learning.

        Args:
            errors: (B, B) error matrix

        Returns:
            Dictionary with diagnostic metrics
        """
        batch_size = errors.shape[0]

        # Basic statistics
        diagonal_errors = torch.diag(errors)
        off_diagonal_mask = ~torch.eye(batch_size, device=errors.device, dtype=torch.bool)
        off_diagonal_errors = errors[off_diagonal_mask]

        diagnostics = {
            'diagonal_error_mean': diagonal_errors.mean().item(),
            'diagonal_error_std': diagonal_errors.std().item(),
            'off_diagonal_error_mean': off_diagonal_errors.mean().item(),
            'off_diagonal_error_std': off_diagonal_errors.std().item(),
            'error_separation': (off_diagonal_errors.mean() - diagonal_errors.mean()).item(),
            'contrastive_accuracy': self.compute_accuracy(errors).item(),
            'temperature': self.temp.item(),
            'scale': self.scale.item(),
        }

        return diagnostics

    def forward_with_diagnostics(self, errors: torch.Tensor) -> tuple[torch.Tensor, dict]:
        """
        Compute loss and return diagnostics.

        Args:
            errors: (B, B) error matrix

        Returns:
            Tuple of (loss, diagnostics_dict)
        """
        loss = self.forward(errors)
        diagnostics = self.get_diagnostics(errors)

        return loss, diagnostics


class SoftREPATrainer(nn.Module):
    """
    SoftREPA-style trainer for robot action diffusion.
    Implements contrastive learning for action-observation alignment.
    """

    def __init__(self, diffusion_model: DiffusionModel_DC):
        super().__init__()
        self.diffusion_model = diffusion_model
        self.device = get_device_from_parameters(diffusion_model)
        self.dtype = get_dtype_from_parameters(diffusion_model)

    def forward(
        self,
        batch: dict[str, torch.Tensor],
        use_dc: bool = True
    ) -> torch.Tensor:
        """
        Forward pass for contrastive learning.

        Args:
            batch: Dictionary containing 'action' and observation data
            use_dc: whether to use DC tokens

        Returns:
            (B, B) error matrix for contrastive learning
        """
        # Use the DiffusionModel_DC's contrastive error computation
        error_matrix = self.diffusion_model.compute_contrastive_error(batch, use_dc=use_dc)

        return error_matrix


# 注意：我们直接使用DiffusionModel中现有的noise_scheduler，
# 不需要重新实现DDPMScheduler，因为DiffusionModel已经有了完整的调度器


class TransformerDCInference:
    """
    Inference engine for DiffusionModel_DC with DC tokens.
    Supports both standard sampling and DC-enhanced sampling.
    """

    def __init__(self, diffusion_model: DiffusionModel_DC, num_inference_steps: int = 50):
        self.diffusion_model = diffusion_model
        self.num_inference_steps = num_inference_steps
        self.device = get_device_from_parameters(diffusion_model)

        # Use the existing noise scheduler from DiffusionModel
        self.noise_scheduler = diffusion_model.noise_scheduler

        # Create inference timesteps
        self.timesteps = torch.linspace(
            diffusion_model.noise_scheduler.config.num_train_timesteps - 1, 0,
            num_inference_steps, dtype=torch.long
        )

    def sample(
        self,
        batch: dict[str, torch.Tensor],
        use_dc: bool = True,
        generator: Optional[torch.Generator] = None
    ) -> torch.Tensor:
        """
        Generate action sequences using DDPM sampling.

        Args:
            batch: Dictionary containing observation data (same format as training)
            use_dc: whether to use DC tokens during sampling
            generator: random number generator for reproducibility

        Returns:
            (B, T, action_dim) generated action sequences
        """
        # Prepare global conditioning using existing method
        global_cond = self.diffusion_model._prepare_global_conditioning(batch)
        batch_size = global_cond.shape[0]

        # Initialize with random noise
        actions = torch.randn(
            batch_size,
            self.diffusion_model.config.horizon,
            self.diffusion_model.config.action_feature.shape[0],
            device=self.device,
            dtype=get_dtype_from_parameters(self.diffusion_model),
            generator=generator
        )

        # Set DC usage
        if hasattr(self.diffusion_model.net, 'use_dc'):
            self.diffusion_model.net.use_dc = use_dc

        # Denoising loop
        for t in self.timesteps:
            timestep_batch = torch.full((batch_size,), t, device=self.device, dtype=torch.long)

            # Predict noise
            with torch.no_grad():
                noise_pred = self.diffusion_model.net(
                    actions, timestep_batch, global_cond=global_cond
                )

            # Update sample using noise scheduler
            actions = self.noise_scheduler.step(noise_pred, t.item(), actions, generator=generator).prev_sample

        return actions

    def sample_with_guidance(
        self,
        batch: dict[str, torch.Tensor],
        guidance_scale: float = 1.0,
        use_dc: bool = True,
        generator: Optional[torch.Generator] = None
    ) -> torch.Tensor:
        """
        Generate action sequences with classifier-free guidance.

        Args:
            batch: Dictionary containing observation data
            guidance_scale: strength of guidance (1.0 = no guidance)
            use_dc: whether to use DC tokens
            generator: random number generator

        Returns:
            (B, T, action_dim) generated action sequences
        """
        if guidance_scale == 1.0:
            return self.sample(batch, use_dc, generator)

        # Prepare global conditioning
        global_cond = self.diffusion_model._prepare_global_conditioning(batch)
        batch_size = global_cond.shape[0]

        # Initialize with random noise
        actions = torch.randn(
            batch_size,
            self.diffusion_model.config.horizon,
            self.diffusion_model.config.action_feature.shape[0],
            device=self.device,
            dtype=get_dtype_from_parameters(self.diffusion_model),
            generator=generator
        )

        # Create unconditional conditions (zeros)
        uncond_global_cond = torch.zeros_like(global_cond)

        # Set DC usage
        if hasattr(self.diffusion_model.net, 'use_dc'):
            self.diffusion_model.net.use_dc = use_dc

        # Denoising loop with guidance
        for t in self.timesteps:
            timestep_batch = torch.full((batch_size,), t, device=self.device, dtype=torch.long)

            with torch.no_grad():
                # Conditional prediction
                cond_noise_pred = self.diffusion_model.net(
                    actions, timestep_batch, global_cond=global_cond
                )

                # Unconditional prediction
                uncond_noise_pred = self.diffusion_model.net(
                    actions, timestep_batch, global_cond=uncond_global_cond
                )

                # Apply classifier-free guidance
                noise_pred = uncond_noise_pred + guidance_scale * (cond_noise_pred - uncond_noise_pred)

            # Update sample
            actions = self.noise_scheduler.step(noise_pred, t.item(), actions, generator=generator).prev_sample

        return actions


def create_diffusion_model_dc(
    config: DiffusionConfig,
    n_dc_tokens: int = 4,
    n_dc_layers: int = 6,
    use_dc_t: bool = True,
    pretrained_model_path: Optional[str] = None
) -> DiffusionModel_DC:
    """
    Factory function to create a DiffusionModel_DC.

    Args:
        config: DiffusionConfig for the model
        n_dc_tokens: number of DC tokens per layer
        n_dc_layers: number of layers to apply DC tokens
        use_dc_t: whether to use time-dependent DC tokens
        pretrained_model_path: path to pretrained DiffusionModel checkpoint (optional)

    Returns:
        Configured DiffusionModel_DC
    """
    if pretrained_model_path is not None:
        return DiffusionModel_DC.from_pretrained(
            pretrained_model_path=pretrained_model_path,
            config=config,
            n_dc_tokens=n_dc_tokens,
            n_dc_layers=n_dc_layers,
            use_dc_t=use_dc_t
        )
    else:
        return DiffusionModel_DC(
            config=config,
            n_dc_tokens=n_dc_tokens,
            n_dc_layers=n_dc_layers,
            use_dc_t=use_dc_t
        )


class DiffusionPolicy_DC(DiffusionPolicy):
    """
    DiffusionPolicy with DC tokens for contrastive learning.

    This is the main policy class that should be used with make_policy.
    It wraps DiffusionModel_DC and provides the PreTrainedPolicy interface.
    """

    config_class = DiffusionConfig  # Will be updated to DiffusionDCConfig
    name = "diffusion_dc"

    def __init__(
        self,
        config: DiffusionConfig,
        dataset_stats: dict[str, dict[torch.Tensor]] | None = None,
    ):
        """Initialize DiffusionPolicy_DC with DC model."""
        # Initialize parent class but don't create the diffusion model yet
        super().__init__(config, dataset_stats)

        # Replace the standard diffusion model with DC version
        self.diffusion = DiffusionModel_DC(config)

    def compute_contrastive_error(self, batch, use_dc=True):
        """Delegate to the underlying DC model."""
        return self.diffusion.compute_contrastive_error(batch, use_dc)
